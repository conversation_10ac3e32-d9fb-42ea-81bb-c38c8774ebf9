import { create } from 'zustand'

// Helper
import { $http } from '@/utils/http'

// Types
import type { IServerMedia } from '@/utils/types'

export interface IFile {
  id: number
  filename: string
  url: string
  type: string | null
  size: string
  name: string
  extension: string
  created_at: string
}

export interface IUser {
  id: number
  name: string
  country_code: string
  email: string
  phone: string
  address: string | null
  image?: IServerMedia
  role: string
}

type TUserLogin = {
  token: string
  admin: IUser
}

interface Actions {
  login: (data: { email: string; password: string }) => Promise<unknown>
  logout: () => void
  update: (data: any) => Promise<unknown>
}

interface State {
  token: string | null
  userData: IUser | null
}

export const useAuth = create<State & Actions>((set) => ({
  token: localStorage.getItem('token'),
  userData: localStorage.getItem('userData')
    ? (JSON.parse(localStorage.getItem('userData') || '') as IUser | null)
    : null,
  image: localStorage.getItem('image') || null,
  async login(data) {
    const res = await $http.post<{
      data: { data: TUserLogin }
    }>({
      url: 'auth/login',
      data: {
        password: data.password,
        email: data?.email,
        // token: localStorage.getItem(environment.DEVICE_TOKEN_LOCALSTORAGE_KEY),
      },
      isFormData: true,
    })

    const { token, admin } = res.data.data
    set((state) => ({ ...state, userData: admin, token }))
    localStorage.setItem('token', token)
    localStorage.setItem('userData', JSON.stringify(admin))
    return Promise.resolve()
  },
  async logout() {
    set((state) => ({ ...state, token: null, userData: null }))
    localStorage.removeItem('token')
    localStorage.removeItem('userData')
  },

  async update(userData: any) {
    localStorage.setItem('userData', JSON.stringify(userData))
    set((state) => ({ ...state, userData }))
  },
}))
