'use server';

import { getServerAuthSession } from '@/config/auth';
import { env } from '@/config/environment';

interface IProps {
  path: string;
  searchParams?: Record<string, any>;
}

//TODO:That's need to be refactored and handel un authored {Error}
export default async function getAction({ path, searchParams }: IProps) {
  try {
    const user = await getServerAuthSession();

    const next = { tags: [path || ''] };
    const url = `${env.BASE_API}${path}?${new URLSearchParams(searchParams)}`;
    const headers: any = user?.user.token && {
      Authorization: `Bearer ${user?.user.token}`,
    };

    const res = await fetch(url, { headers, next });
    const jsonRes = await res.json();

    if (res.ok) {
      return jsonRes.data;
    } else {
      if (res.status === 401) {
        throw new Error(jsonRes.message);
      } else {
        throw new Error('Not Found');
      }
    }
  } catch (error) {
    throw new Error('Faild');
  }
}
