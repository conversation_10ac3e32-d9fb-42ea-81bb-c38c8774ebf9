'use client';

import { NextIntlClientProvider } from 'next-intl';

// Types
import type { PropsWithChildren } from 'react';

const IntlClientProvider = ({ locale, messages, children }: PropsWithChildren<any>) => {
  function onError(error: any) {
    if (error.code === 'MISSING_MESSAGE') return;
    console.error(error);
  }
  return (
    <NextIntlClientProvider locale={locale} messages={messages} onError={onError} timeZone="Asia/Damascus">
      {children}
    </NextIntlClientProvider>
  );
};

export default IntlClientProvider;
