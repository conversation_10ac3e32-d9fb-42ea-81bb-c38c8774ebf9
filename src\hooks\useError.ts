'use client';

// Utils
import { type IToast } from '@/components/ui/Toast';
import { observer } from '@/utils/observer';

// Types
import type { IError } from './types';

const useError = () => {
  const fireToast = (error: IError, type: IToast['type']) => {
    const messages: string[] = [];

    // TODO: handle error logic based on backend response and you can also add description to every message

    for (let index = 0; index < messages.length; index++) {
      observer.fire('notify', {
        type: type ?? 'error',
        message: messages[index],
      });
    }
  };

  return {
    fireToast,
  };
};

export default useError;
