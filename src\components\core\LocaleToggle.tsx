// Components
import { useCallback } from 'react'
import { Button } from '@/components/ui/button'

// Hooks
import { useTranslation } from 'react-i18next'

// Environments
import env from '@/config/env'
import ArabicIcon from '@/assets/arabic.svg'
import EnglishIcon from '@/assets/english.svg'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'

const LocaleToggle = () => {
  const { i18n } = useTranslation()

  const handleSwitchLocale = useCallback(
    (locale: 'ar' | 'en') => {
      i18n.changeLanguage(locale)

      localStorage.setItem(env.LOCALE_KEY, locale)

      document.documentElement.lang = locale
      document.documentElement.dir = i18n.dir(locale)
    },
    [i18n]
  )

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          {i18n.language == 'ar' && <img src={ArabicIcon} />}
          {i18n.language == 'en' && <img src={EnglishIcon} />}
          <span className="sr-only">Language theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleSwitchLocale('en')}>
          <img src={EnglishIcon} /> EN
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSwitchLocale('ar')}>
          <img src={ArabicIcon} /> AR
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export { LocaleToggle }
