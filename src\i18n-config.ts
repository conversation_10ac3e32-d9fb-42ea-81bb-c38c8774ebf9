import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

export const i18n = {
  defaultLocale: 'en',
  locales: ['en', 'ar'],
} as const;

export default getRequestConfig(async ({ locale }) => {
  if (!i18n.locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`./locales/${locale}.json`)).default,
  };
});

export type Locale = (typeof i18n)['locales'][number];
