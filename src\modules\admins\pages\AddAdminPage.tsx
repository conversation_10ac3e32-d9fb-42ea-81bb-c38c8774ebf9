import { FormWrapper } from '@/components/core/FormWrapper'
import { AddAdminSchema } from '../schema'
import useAdminActions from '../containers/useAdminActions'
import { Admin } from '../types'
import { FormInput } from '@/components/form/FormInput'
import { Button } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { FormBodyLayout } from '@/components/form/FormBodyLayout'
import { FormActionsLayout } from '@/components/form/FormActionsLayout'
import { FormSelect } from '@/components/form/FormSelect'
import { FormCombobox } from '@/components/form/FormCombobox'
import { FormFileUpload } from '@/components/form/FormFileUpload'
import { FormRadioInput } from '@/components/form/FormRadioInput'

import { FormSwitch } from '@/components/form/FormSwitch'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { FormDatePicker } from '@/components/form/FormDatePicker'

import PageHeader from '@/components/shared/PageHeader'
import { UserIcon } from 'lucide-react'

import { FormMultiSelect } from '@/components/form/FormMultiSelect'
import { FormTextEditor } from '@/components/form/FormTextEditor'
import { FormTextArea } from '@/components/form'

const AddAdminPage = () => {
  const { t } = useTranslation()
  const { defaultValues, onSubmit, genders, categories, frameworksList, options } = useAdminActions()

  return (
    <FormWrapper<Admin> schema={AddAdminSchema} defaultValues={defaultValues} onSubmit={onSubmit}>
      <PageHeader title={t('navbar.add_admin')} description={t('navbar.add_admin_desc')} />
      <FormBodyLayout>
        <FormInput
          containerClassName="flex"
          prefix={
            <Button type="button" variant={'ghost'}>
              <UserIcon className="size-4" />
            </Button>
          }
          name="name"
          placeholder={t('label.name')}
        />
        <FormInput name="email" type="email" placeholder={t('label.email')} />
        <FormInput name="age" type="number" placeholder={t('label.age')} />

        <FormMultiSelect
          name="frameworks"
          data={frameworksList}
          valueKey="value"
          labelKey="label"
          placeholder={t('label.select_framework')}
        />

        <FormCombobox
          data={categories}
          labelKey="label"
          valueKey="value"
          name="category"
          placeholder={t('label.select_category')}
        />
        <FormSelect
          data={genders}
          labelKey="label"
          valueKey="value"
          name="gender"
          placeholder={t('label.select_gender')}
        />

        <FormPasswordInput name="password" placeholder={t('label.password')} />
        <FormPasswordInput name="confirm_password" placeholder={t('label.password_confirmation')} />

        <FormDatePicker name="birthDate" placeholder={t('label.birth_date')} mode="single" />

        <FormDatePicker name="date_range" placeholder={t('label.select_date_range')} mode="range" />

        <div className="col-span-full ">
          <FormTextEditor name="ahmed" label="hello" placeholder="ahmed" />
        </div>
        <FormDatePicker name="birthDate" placeholder={t('label.birth_date')} mode="single" />
        <FormDatePicker name="date_range" placeholder={t('label.select_date_range')} mode="range" />
        <FormDatePicker name="days" placeholder={t('label.select_days')} mode="multiple" />

        <FormTextArea containerClassName="!col-span-full" name="description" placeholder={t('label.description')} />

        <FormRadioInput label={t('label.status')} name="status" options={options} />

        <FormSwitch name="is_active" label={t('label.is_active')} />

        <div className="col-span-full">
          <FormFileUpload multiple name="image" maxSize={5} maxLength={1} accept="image/*" />
        </div>

        <div className="col-span-full">
          <FormFileUpload multiple name="multiple_images" maxSize={5} maxLength={5} accept="image/*" />
        </div>

        <FormActionsLayout>
          <Button variant={'outline'} className="md:min-w-[150px]  ">
            {t('button.back')}
          </Button>
          <Button type="submit" className="md:min-w-[150px] bg-sky-700 hover:bg-sky-800">
            {t('button.save')}
          </Button>
        </FormActionsLayout>
      </FormBodyLayout>
    </FormWrapper>
  )
}

export default AddAdminPage
