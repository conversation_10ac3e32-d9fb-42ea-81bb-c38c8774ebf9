import { Suspense, useEffect } from 'react'
import { Outlet, useLocation, useNavigate } from 'react-router'
import Loading from '@/components/core/Loading'
import { ThemeProvider } from '@/components/theme/ThemeProvider'
import { Routes } from '@/routes/routes'

const AppLayout = () => {
  const navigate = useNavigate()
  const { pathname } = useLocation()

  useEffect(() => {
    if (pathname == '/') navigate(Routes.dashboard)
  }, [])

  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <div className="min-h-screen min-w-full flex items-center justify-center">
        <Suspense fallback={<Loading />} key={location.pathname}>
          <Outlet />
        </Suspense>
      </div>
    </ThemeProvider>
  )
}

export default AppLayout
