'use client';

import { useState } from 'react';

import { MetaOption } from '@/components/ui/Table/types';
// Config
import axiosInstance from '@/config/axios';

// types
import type { IApiHookReturn, IApiProps, IApiReturn } from './types';

const useApi = () => {
  const [loading, setLoading] = useState(false);

  const api = async <T>({ url, method, body, params, headers, noLoading }: IApiProps): Promise<IApiReturn<T>> => {
    try {
      !noLoading && setLoading(true);

      const res: any = await axiosInstance({
        url,
        method,
        params,
        headers,
        data: body,
      });
      const { data, meta } = res.data;
      return { meta, data, error: null };
    } catch (error: any) {
      return { data: null, error };
    } finally {
      !noLoading && setLoading(false);
    }
  };

  return {
    api,
    loading,
  };
};

export default useApi;
