import { CountryDropdown } from '@/components/ui/country-dropdown'
import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { FormMessage } from '@/components/ui/form'
import { useFormWrapperContext } from '@/components/core/FormWrapper'
import { ReactNode } from 'react'

export function FormPhoneInput({
  phoneName,
  countryName,
  placeholder,
}: {
  phoneName: string
  countryName: string
  placeholder?: string
}) {
  const { errors } = useFormWrapperContext()
  const { setValue, getValues, watch } = useFormContext()

  return (
    <div className="flex flex-col gap-2">
      <div className="flex" dir="ltr">
        <CountryDropdown
          slim
          onChange={(value) => setValue(countryName, value)}
          defaultValue={getValues(countryName)}
        />
        <Input
          slim
          type="number"
          name={phoneName}
          value={watch(phoneName)}
          placeholder={placeholder}
          onChange={(e) => setValue(phoneName, e.target.value)}
        />
      </div>
      {errors[phoneName] && <FormMessage>{errors[phoneName].message as ReactNode}</FormMessage>}
      {errors[countryName] && <FormMessage>{errors[countryName].message as ReactNode}</FormMessage>}
    </div>
  )
}
