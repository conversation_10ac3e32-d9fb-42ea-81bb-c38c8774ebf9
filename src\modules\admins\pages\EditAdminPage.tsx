import { FormWrapper } from '@/components/core/FormWrapper'
import { AddAdminSchema } from '../schema'
import useAdminActions from '../containers/useAdminActions'
import { Admin } from '../types'
import { FormInput } from '@/components/form/FormInput'
import { Button } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { FormBodyLayout } from '@/components/form/FormBodyLayout'
import { FormActionsLayout } from '@/components/form/FormActionsLayout'
import { FormSelect } from '@/components/form/FormSelect'
import { FormCombobox } from '@/components/form/FormCombobox'
import { FormFileUpload } from '@/components/form/FormFileUpload'
import { FormRadioInput } from '@/components/form/FormRadioInput'

import { FormSwitch } from '@/components/form/FormSwitch'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { FormDatePicker } from '@/components/form/FormDatePicker'

import PageHeader from '@/components/shared/PageHeader'
import { ChevronDownIcon, UserIcon } from 'lucide-react'

import { FormMultiSelect } from '@/components/form/FormMultiSelect'
import { useParams } from 'react-router'

const EditAdminPage = () => {
  const { t } = useTranslation()
  const { defaultValues, onSubmit, genders, categories, frameworksList, options } = useAdminActions()
  const { id } = useParams()

  return (
    <FormWrapper<Admin> schema={AddAdminSchema} defaultValues={defaultValues} onSubmit={onSubmit}>
      <PageHeader title={`${id}`} description="edit page" />
      <FormBodyLayout>
        <FormInput
          containerClassName="flex"
          prefix={<UserIcon className="size-4" />}
          suffix={<ChevronDownIcon className="size-4" />}
          name="name"
          placeholder={t('label.name')}
        />
        <FormInput name="email" type="email" placeholder={t('label.email')} />
        <FormInput name="age" type="number" placeholder={t('label.age')} />
        <FormSelect data={genders} labelKey="label" valueKey="value" name="gender" placeholder={t('label.gender')} />

        <FormCombobox
          data={categories}
          labelKey="label"
          valueKey="value"
          name="category"
          placeholder={t('label.category')}
          onChange={(value) => {
            console.log(value?.value)
          }}
          onSearch={(value) => {
            console.log(value)
          }}
          onToggle={(isOpen) => {
            console.log(isOpen)
          }}
        />

        <FormCombobox
          data={categories}
          labelKey="label"
          valueKey="value"
          name="categories"
          placeholder={t('label.category')}
        />

        <FormMultiSelect
          name="frameworks"
          data={frameworksList}
          valueKey="value"
          labelKey="label"
          placeholder={t('label.frameworks')}
        />

        <FormRadioInput label={t('label.status')} name="status" options={options} />

        <FormSwitch name="is_active" label={t('label.is_active')} />
        <FormPasswordInput name="password" placeholder={t('label.password')} />
        <FormPasswordInput name="password_confirmation" placeholder={t('label.password_confirmation')} />
        <hr />

        <div className="col-span-full">
          <FormFileUpload multiple name="image" maxSize={0.11111} maxLength={1} accept="application/pdf" />
        </div>

        <FormDatePicker name="birthDate" placeholder={t('label.birth_date')} mode="single" />
        <FormDatePicker name="date_range" placeholder={t('label.select_date_range')} mode="range" />
        <FormDatePicker name="days" placeholder={t('label.select_days')} mode="multiple" />

        <FormActionsLayout>
          <Button variant={'secondary'}>{t('button.back')}</Button>
          <Button type="submit">{t('button.save')}</Button>
        </FormActionsLayout>
      </FormBodyLayout>
    </FormWrapper>
  )
}

export default EditAdminPage
