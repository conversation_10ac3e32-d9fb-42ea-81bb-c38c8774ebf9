import { array, date, object, mixed, string } from 'yup'
import { numberTransform, passwordSchema } from '@/lib/schema'

export const AddAdminSchema = object().shape({
  name: string().required(),
  description: string().required(),
  email: string().email().required(),
  age: numberTransform().required(),
  gender: string().required(),
  category: string().transform((value) => {
    if (typeof value === 'object' && value?.value) {
      return value.value
    }
    return value
  }),
  categories: string().transform((value) => {
    if (typeof value === 'object' && value?.value) {
      return value.value
    }
    return value
  }),

  birthDate: date().required(),
  days: array().of(date()).required(),
  date_range: object()
    .shape({
      from: date().required(),
      to: date().required(),
    })
    .required(),
  status: string().required(),
  password: passwordSchema(),
  image: mixed().nullable(),
})
