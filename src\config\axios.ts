import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { getCookie } from 'cookies-next';
import { getSession, signOut } from 'next-auth/react';

import { observer } from '@/utils/observer';
import { env } from './environment';

const axiosInstance: AxiosInstance = axios.create({
  baseURL: env.BASE_API,
});

axiosInstance.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  // const session = await getSession()
  config.headers.Authorization = `Bearer 562|a0g5rZ3GZRJ1af6kI2hp3H0L1Cd5mLN2XFlIgague47d9b8e`;
  config.headers['Accept-language'] = getCookie(env.LANGUAGE) ?? 'en';
  return config;
});

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    const { method, url } = response.config;
    if (method === 'post' || method === 'put' || method === 'delete') {
      if (response.data.message && url !== 'media') {
        observer.fire('notify', {
          type: 'success',
          message: response.data.message,
        });
      }
    }

    return response;
  },

  (error: AxiosError) => {
    const data = error.response?.data as {
      message: string;
      errors: any;
      code: number;
    };

    if (error?.response?.status == 401 || data?.code == 401) {
      signOut({
        callbackUrl: '/auth/login',
      });
    }

    data?.message &&
      observer.fire('notify', {
        type: 'error',
        message: data.message,
      });

    return Promise.reject(error);
  }
);

export default axiosInstance;
