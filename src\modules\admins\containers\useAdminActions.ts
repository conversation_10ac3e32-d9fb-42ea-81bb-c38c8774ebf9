import { Admin } from '../types'
import { serialize } from 'object-to-formdata'
import { printFormData } from '@/utils/formData'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Turtle } from 'lucide-react'
import axiosInstance from '@/config/axios'
import { toast } from 'sonner'

const defaultValues = {
  name: '',
  description: '',
  email: '',
  age: null,
  gender: '',
  category: null,
  frameworks: [],
  birthDate: null,
  days: null,
  date_range: null,
  ahmed: '',
  status: '',
  password: '',
  confirm_password: '',
  image: {
    id: 396,
    name: 'Zamale<PERSON>',
    path: 'https://mo-ragheb.engsaas-b.geexar.dev/tenancy/assets/396/Zamalek.png',
    type: 'image/png',
    extension: 'png',
  },
  multiple_images: [
    {
      id: 396,
      name: 'Zamalek',
      path: 'https://mo-ragheb.engsaas-b.geexar.dev/tenancy/assets/396/Zamalek.png',
      type: 'image/png',
      extension: 'png',
    },
    {
      id: 308,
      name: '<PERSON><PERSON>lek-2',
      path: 'https://mo-ragheb.engsaas-b.geexar.dev/tenancy/assets/308/Zamalek-2.png',
      type: 'image/png',
      extension: 'png',
    },
  ],
}

const genders = [
  { label: 'male', value: 'male value' },
  { label: 'female', value: 'female value' },
]

const categories = [
  { label: 'cat', value: 'cat value' },
  { label: 'dog', value: 'dog value' },
  { label: 'lion', value: 'lion value' },
]

const frameworksList = [
  { value: 'react', label: 'React', icon: Turtle },
  { value: 'angular', label: 'Angular', icon: Cat },
  { value: 'vue', label: 'Vue', icon: Dog },
  { value: 'svelte', label: 'Svelte', icon: Rabbit },
  { value: 'ember', label: 'Ember', icon: Fish },
]

const options = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3', value: 'option3' },
]

const useAdminActions = () => {
  const dataAdapter = (data: Admin): FormData => {
    const formData = serialize(data, { indices: true })
    return formData
  }

  const onSubmit = (data: Admin) => {
    const formData = dataAdapter(data)
    console.log(data, formData)
    toast.success('Admin created successfully')
    printFormData(formData)
    axiosInstance.post('http://localhost:8000/admins', formData)
  }

  return { onSubmit, defaultValues, genders, categories, frameworksList, options }
}

export default useAdminActions
