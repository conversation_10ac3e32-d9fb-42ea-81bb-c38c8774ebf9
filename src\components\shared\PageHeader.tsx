import { ReactNode } from 'react'

const PageHeader = ({
  title,
  description,
  renderSuffix,
}: {
  title: string
  description: string
  renderSuffix?: () => ReactNode
}) => {
  return (
    <header className="flex justify-between gap-4 mb-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl">{title}</h1>
        <p className="text-muted-foreground">{description}</p>
      </div>
      {renderSuffix && renderSuffix()}
    </header>
  )
}

export default PageHeader
