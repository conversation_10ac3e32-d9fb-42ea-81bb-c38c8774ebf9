import { Suspense, useEffect } from 'react'
import DashboardSkeleton from '../components/DashboardSkeleton'
import { ChartAreaLegend } from '../components/AreaChart'
import { ChartRadarDots } from '../components/RadarChart'
import CardsSection from '../components/CardsSection'
import { Button } from '@/components/ui/button'
import { useBreadcrumb } from '@/components/layouts/PrivateLayout/Breadcrumb'

const DashboardPage = () => {
  const { updateBreadCrumbParams, setBreadcrumbItems } = useBreadcrumb()

  useEffect(() => {
    updateBreadCrumbParams({ id: 'Esalm' })
    setBreadcrumbItems([
      { label: 'moahmed', to: '/dashboard' },
      <Button
        variant="outline"
        onClick={() => {
          console.log('Bread Crumb button clicked')
        }}
      >
        New
      </Button>,
    ])
  }, [updateBreadCrumbParams])

  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <div className="grid auto-rows-min gap-4 lg:grid-cols-2 xl:grid-cols-4">
        <CardsSection />
      </div>
      <div className="grid grid-cols-12 auto-rows-min gap-4 max-h-fit">
        <div className="col-span-full lg:col-span-8">
          <ChartAreaLegend />
        </div>
        <div className="col-span-full lg:col-span-4 flex flex-col gap-4">
          <ChartRadarDots />
        </div>
      </div>
    </Suspense>
  )
}

export default DashboardPage
