'use client';

import { useTheme } from 'next-themes';
import { memo, useEffect, useState } from 'react';

import { cn } from '@/utils/cn';

const ThemeSwitch = () => {
  const [mounted, setMounted] = useState(false);
  const { setTheme, resolvedTheme } = useTheme();
  const isDark = resolvedTheme && resolvedTheme === 'dark';

  useEffect(() => setMounted(true), []);

  const switchThemeHandler = () => {
    if (isDark) {
      setTheme('light');
    } else {
      setTheme('dark');
    }
  };

  if (!mounted) return <></>;

  return (
    <div className="flex gap-2 items-center">
      <svg
        width="22"
        height="22"
        viewBox="0 0 22 22"
        fill="none"
        className={isDark ? 'fill-primary' : 'fill-[#BBC3CF]'}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8 6C7.99965 7.40007 8.36673 8.77571 9.06457 9.98947C9.76241 11.2032 10.7666 12.2126 11.9767 12.9167C13.1868 13.6208 14.5605 13.995 15.9606 14.0019C17.3606 14.0088 18.738 13.6482 19.955 12.956C19.474 18.03 15.2 22 10 22C4.477 22 0 17.523 0 12C0 6.8 3.97 2.526 9.044 2.045C8.35756 3.24994 7.99768 4.61325 8 6ZM2 12C2 14.1217 2.84285 16.1566 4.34315 17.6569C5.84344 19.1571 7.87827 20 10 20C11.4135 19.9999 12.8017 19.6256 14.0237 18.9151C15.2456 18.2047 16.2577 17.1834 16.957 15.955C16.641 15.985 16.321 16 16 16C10.477 16 6 11.523 6 6C6 5.679 6.015 5.36 6.045 5.043C4.81664 5.74232 3.79533 6.75439 3.08486 7.97633C2.37438 9.19827 2.00008 10.5865 2 12ZM16.164 2.291L17 2.5V3.5L16.164 3.709C15.8124 3.79693 15.4913 3.97875 15.235 4.23503C14.9788 4.4913 14.7969 4.8124 14.709 5.164L14.5 6H13.5L13.291 5.164C13.2031 4.8124 13.0212 4.4913 12.765 4.23503C12.5087 3.97875 12.1876 3.79693 11.836 3.709L11 3.5V2.5L11.836 2.291C12.1874 2.20291 12.5083 2.02102 12.7644 1.76475C13.0205 1.50849 13.2021 1.18748 13.29 0.836L13.5 0H14.5L14.709 0.836C14.7969 1.1876 14.9788 1.5087 15.235 1.76497C15.4913 2.02125 15.8124 2.20307 16.164 2.291ZM21.164 7.291L22 7.5V8.5L21.164 8.709C20.8124 8.79693 20.4913 8.97875 20.235 9.23503C19.9788 9.4913 19.7969 9.8124 19.709 10.164L19.5 11H18.5L18.291 10.164C18.2031 9.8124 18.0212 9.4913 17.765 9.23503C17.5087 8.97875 17.1876 8.79693 16.836 8.709L16 8.5V7.5L16.836 7.291C17.1876 7.20307 17.5087 7.02125 17.765 6.76497C18.0212 6.5087 18.2031 6.1876 18.291 5.836L18.5 5H19.5L19.709 5.836C19.7969 6.1876 19.9788 6.5087 20.235 6.76497C20.4913 7.02125 20.8124 7.20307 21.164 7.291Z"
          fill={'inherit'}
        />
      </svg>
      <div
        role="button"
        tabIndex={0}
        onClick={switchThemeHandler}
        className="h-[28px] w-[48px] relative rounded-[40px] dark:bg-primary bg-[#E2E8F0]"
      >
        <div
          className={cn(
            'shadow-4xl transition-all ease-out duration-300 absolute left-[2px] w-[24px] h-[24px] top-[2px] rounded-full bg-white',
            isDark && 'left-[20px]'
          )}
        />
      </div>
    </div>
  );
};

export default memo(ThemeSwitch);
