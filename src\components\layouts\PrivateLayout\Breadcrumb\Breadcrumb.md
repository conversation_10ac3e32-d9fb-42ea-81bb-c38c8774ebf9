# Breadcrumb Components Documentation

This document provides an overview and usage details for the components located in the `Breadcrumb` folder. These components are used to display and manage breadcrumb navigation within the application, supporting both automatic and programmatic breadcrumb updates.

---

## Components

### 1. `Breadcrumb`

**Path:** `Breadcrumb.tsx`

**Description:**
The main breadcrumb navigation component. It automatically generates breadcrumb items based on the current route path, using the `useLocation` hook from React Router. Each segment of the path is translated using the `useTranslation` hook and rendered as a clickable breadcrumb link.
The component also listens for external updates to the breadcrumb items via a custom observer pattern, allowing programmatic control of the breadcrumb trail.

**Key Features:**

- Auto-generates breadcrumbs from the current URL.
- Supports translation of breadcrumb labels.
- Allows external components to override breadcrumb items using the observer.
- Renders custom React elements as breadcrumb items if provided.

**Usage Example:**

```tsx
import { Breadcrumb } from './Breadcrumb'

// Inside a layout or page component
;<Breadcrumb />
```

---

## Observer Pattern

Both `Breadcrumb` use a custom observer utility (`observer`) to communicate breadcrumb updates.

- `Breadcrumb` subscribes to the `'set_breadcrumb_items'` event to update its state.
- `PageBreadcrumb` fires the `'set_breadcrumb_items'` event with new items.

This pattern decouples the breadcrumb display from the logic that determines its content, allowing for flexible and dynamic breadcrumb management.

---

## Summary Table

| Component    | Purpose                                        | Typical Usage               |
| ------------ | ---------------------------------------------- | --------------------------- |
| `Breadcrumb` | Displays breadcrumb navigation, auto or custom | In layout/header components |

---

## Example Integration

```tsx
// In your main layout
<Breadcrumb />
```

---

For further customization, you can provide React elements as breadcrumb items, or extend the observer pattern for more advanced scenarios.
