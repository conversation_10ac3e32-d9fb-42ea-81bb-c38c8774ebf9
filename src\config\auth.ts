// ** Third Party Imports
import axios from 'axios';
import { AuthOptions, getServerSession } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

import { env } from '@/config/environment';

let expires = 60 * 60; // ** 1hour default

export const authOptions: AuthOptions = {
  // JWT Secret from env variable
  secret: env.NEXT_PUBLIC_SECRET,

  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      type: 'credentials',
      credentials: {},
      async authorize(credentials) {
        const { email, password, remember, type } = credentials as any;
        try {
          const res: any = await axios({
            method: 'post',
            url: `${env.BASE_API}/login`,
            data: {
              email,
              password,
              type,
            },
          });

          const user = res?.data?.data;

          if (res.status === 200 && user) {
            if (remember === 'true') {
              expires = 30 * 24 * 60 * 60; //** 30 day
            }

            return user;
          }

          return null;
        } catch (err) {
          const { response } = err as any;
          throw response?.data?.Error;
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt' as const,
    maxAge: expires, // ** 1 hour
  },

  pages: {
    signIn: '/auth/login',
    signOut: '/',
    error: '/auth/login',
  },

  callbacks: {
    async jwt({ token, user, session, trigger }: any) {
      if (user) {
        /*
         * For adding custom parameters to user in session, we first need to add those parameters
         * in token which then will be available in the `session()` callback
         */
        token.user = user;
      }

      if (trigger === 'update') {
        token.user = session;
      }

      // The Token takes it's value from user data when logged in. If the session has been modified, We have to update the token to match the session value.
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        // ** Add custom params to user in session which are added in `jwt()` callback via `token` parameter
        session.user = token.user;
      }

      return session;
    },
  },
};

export const getServerAuthSession = () => getServerSession(authOptions);
