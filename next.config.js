/** @type {import('next').NextConfig} */

const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin('./src/i18n-config.ts');

const nextConfig = {
  reactStrictMode: true,

  experimental: {
    optimizePackageImports: ['react-icons'],
  },

  typescript: {
    ignoreBuildErrors: true,
  },

  env: {
    BASE_API: process.env.BASE_API,
    NEXT_PUBLIC_SECRET: process.env.NEXT_PUBLIC_SECRET,
    NEXT_URL: process.env.NEXT_URL,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    LANGUAGE: process.env.LANGUAGE,
  },
};

module.exports = withNextIntl(nextConfig);

