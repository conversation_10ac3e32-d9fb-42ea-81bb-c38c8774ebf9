import DatePickerFilter from '@/components/filters/DatePickerFilter'
import FilterCombobox from '@/components/filters/FilterCombobox'
import FilterMultiSelect from '@/components/filters/FilterMultiSelect'
import FilterSelect from '@/components/filters/FilterSelect'
import SearchFilter from '@/components/filters/SearchFilter'
import { Badge } from '@/components/ui/badge'
import { Table, TableRow, TableCell } from '@/components/ui/table'
import {
  TableHeader,
  TableProvider,
  TableBody,
  TablePagination,
  TableActionsDropdown,
  TableAction,
} from '@/components/ui/Table/index'
import QueryProvider from '@/store/queryContext/queryContext'
import { useTranslation } from 'react-i18next'

interface User {
  id: string
  name: string
  email: string
  address: string
  status: string
}

const data: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'Active',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'Inactive',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'Active',
  },
  {
    id: '4',
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'Inactive',
  },
  {
    id: '5',
    name: 'John Doe',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'Active',
  },
]

const ListAdminPage = () => {
  const { t } = useTranslation()

  const headers = [
    { title: '#' },
    { title: t('table.name'), sortKey: 'name' },
    { title: t('table.status') },
    { title: t('table.actions') },
  ]
  const cities = [
    { label: 'Tbilisi', id: 1 },
    { label: 'Batumi', id: 2 },
    { label: 'Kutaisi', id: 3 },
  ]

  return (
    <>
      <QueryProvider isRouteQuery>
        <h1>admins list</h1>
        <div className="grid grid-cols-3 md:grid-cols-4 gap-4">
          <SearchFilter name="search" />
          <FilterSelect name="city_id" data={cities} labelKey="label" valueKey="id" />
          <FilterMultiSelect
            data={[
              { label: 'Active', value: 'Active' },
              { label: 'Inactive', value: 'Inactive' },
            ]}
            name="select"
            labelKey="label"
            valueKey="value"
          />
          <FilterCombobox
            name="combo"
            data={[
              { label: 'Active', value: 'Active' },
              { label: 'Inactive', value: 'Inactive' },
            ]}
            labelKey="label"
            valueKey="value"
          />
          <DatePickerFilter name="date" />
        </div>
        <TableProvider<User> data={data}>
          <Table>
            <TableHeader headers={headers} />
            <TableBody
              columnCount={4}
              render={({ item, row_count }) => (
                <TableRow>
                  <TableCell>{row_count}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>
                    <Badge variant={item.status === 'Active' ? 'success' : 'error'}>{item.status}</Badge>
                  </TableCell>
                  <TableActionsDropdown>
                    <TableAction
                      tableActionVariant="View"
                      onClick={() => {
                        console.log(item.id)
                      }}
                    />
                    <TableAction
                      tableActionVariant="Edit"
                      onClick={() => {
                        console.log(item.id)
                      }}
                    />
                    <TableAction
                      tableActionVariant="Delete"
                      onClick={() => {
                        console.log(item.id)
                      }}
                    />
                  </TableActionsDropdown>
                </TableRow>
              )}
            ></TableBody>
          </Table>
          <TablePagination />
        </TableProvider>
      </QueryProvider>
    </>
  )
}

export default ListAdminPage
