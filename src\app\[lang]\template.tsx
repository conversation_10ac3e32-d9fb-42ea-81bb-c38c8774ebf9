'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

import { useQueryStore } from '@/store/query';

export default function Template({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  useEffect(() => {
    if (Object.entries(useQueryStore.getState().query).length > 0) useQueryStore.getState().clearQuery();
  }, [pathname]);
  return <div>{children}</div>;
}
