// React-Query
import { QueryFunction, QueryKey, useQuery, UseQueryOptions } from '@tanstack/react-query'

// Hooks
import { $http } from '@/utils/http.ts'

interface Props<T> {
  reqName?: string
  query?: Record<string, any>
  queryKey: QueryKey
  queryFn?: QueryFunction<T>
  options?: Omit<UseQueryOptions<any, unknown, any, QueryKey>, 'queryKey' | 'queryFn'>
}

export default function useFetch<T>({ queryKey, queryFn, options, reqName, query }: Props<T>) {
  const commonFetchFunction: QueryFunction<T, QueryKey> = async () => {
    try {
      const res = await $http.get<{ data: T }>({
        url: reqName!,
        ...(query && { query }),
      })
      return res.data
    } catch (e) {
      console.log(e)

      // Error Logic Will Handled By Axios Interceptor
      throw e
    }
  }
  const res = useQuery<T, unknown, T>({
    queryKey: [...queryKey, query],
    queryFn: (queryFn ?? commonFetchFunction) as any,
    refetchOnWindowFocus: false,
    ...options,
  })

  return { ...res }
}
