import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useQuery } from '@/store/queryContext/useQueryContext'
import { Root } from '@radix-ui/react-select'
import { X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '../ui/button'
import { Label } from '../ui/label'
import { useTranslation } from 'react-i18next'
import { useRtl } from '@/hooks/useRtl'

export interface FilterSelectProps<T> extends React.ComponentProps<typeof Root> {
  label?: string
  name: string
  data: T[]
  placeholder?: string
  valueKey: keyof T & string
  labelKey: keyof T & string
}

const FilterSelect = <T,>({ label, name, data, valueKey, labelKey, placeholder, ...props }: FilterSelectProps<T>) => {
  const { forwardAddQuery, forwardDeleteQuery, forwardQuery } = useQuery()
  const [selectedValue, setSelectedValue] = useState<string | null>(forwardQuery?.[name] ?? null)
  const { t } = useTranslation()
  const { isRtl } = useRtl()

  useEffect(() => {
    setSelectedValue(forwardQuery?.[name] ?? null)
  }, [forwardQuery, name])

  const handleSelectFilter = (value: string) => {
    setSelectedValue(value)
    forwardAddQuery({ [name]: value })
  }

  const handleRemoveFilter = () => {
    setSelectedValue(null)
    forwardDeleteQuery(name)
  }

  return (
    <>
      {label && <Label htmlFor={`${name}-select`}>{label}</Label>}
      <div className="relative">
        <Select value={selectedValue ?? ''} onValueChange={handleSelectFilter} {...props}>
          <SelectTrigger className="w-full" id={`${name}-select`} dir={isRtl ? 'rtl' : 'ltr'}>
            <SelectValue placeholder={placeholder ?? t('label.select_option')} />
          </SelectTrigger>
          <SelectContent dir={isRtl ? 'rtl' : 'ltr'}>
            {data.map((item, index) => (
              <SelectItem key={`${item[valueKey]}-${index}`} value={String(item[valueKey])}>
                {String(item[labelKey])}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {selectedValue && (
          <Button
            variant="ghost"
            type="button"
            onClick={handleRemoveFilter}
            tabIndex={-1}
            className="absolute end-6 top-1/2 -translate-y-1/2 z-10 p-1 text-muted-foreground hover:bg-transparent"
            aria-label="Remove filter"
          >
            <X className="size-4" />
          </Button>
        )}
      </div>
    </>
  )
}

export default FilterSelect
