'use client';

import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useEffect } from 'react';
import { array, object, string } from 'yup';

import { Each } from '@/components/core/Each';
// import LocaleSwitcher from '@/components/shared/locale-switcher';
import ThemeSwitch from '@/components/shared/ThemeSwitch';
import Dropdown, { DropdownMenu } from '@/components/ui/Dropdown';
import { FormWrapper } from '@/components/ui/Fields/FormWrapper';
import Input from '@/components/ui/Fields/Input';
import Select from '@/components/ui/Fields/Select';
import TextArea from '@/components/ui/Fields/TextArea';
import Switch from '@/components/ui/Switch';
import useApi from '@/hooks/useApi';
import revalidate from '@/services/revalidate';

// import { getTranslations } from 'next-intl/server';

const schema = object({
  gender: array().required(),
  name: string().required(),
  note: string().required(),
});

export default function Home() {
  // const t = await getTranslations('server-component');
  // const genders = [
  //   { text: 'male', value: 'male' },
  //   { text: 'female', value: 'female' },
  // ];

  const { loading, api } = useApi();

  const fetchData = async () => {
    console.log(loading);
    const { data, error } = await api({ method: 'GET', url: '/user/countries' });
    console.log(data);
    if (error) return console.log(error);
    console.log(data);
  };

  const login = async () => {
    await signIn('credentials', {
      email: '<EMAIL>',
      password: '12345678',
      remember: false,
      type: 'user',
      callbackUrl: '/',
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <LocaleSwitcher />
      <ThemeSwitch />
      <FormWrapper
        schema={schema}
        defaultValues={{ gender: null, name: '', note: '' }}
        onSubmit={(data) => console.log(data)}
      >
        <Dropdown>
          <button type="button" id={'button-1'} role="button" aria-controls="menu" className="text-primary">
            hallo mr perfect
          </button>
          <DropdownMenu aria-labelledby={'button-1'} className="flex flex-col gap-3">
            <Each
              of={['esalm', 'moaz']}
              render={(item, i) => (
                <li key={i} role={'presentation'}>
                  <a tabIndex={i + 1} role="menuitem" id={`menuitem-${i + 1}`}>
                    {item}
                  </a>
                </li>
              )}
            />
          </DropdownMenu>
        </Dropdown>
        <p>mr select</p>
        <Select<{ id: number; uuid: string }>
          label="hi"
          placeholder="hi"
          name="gender"
          itemLabel="uuid"
          itemValue="id"
          multiple
          items={[
            { id: 1, uuid: 'hallo' },
            { id: 10, uuid: 'hallow' },
            { id: 20, uuid: 'hallo222' },
            { id: 30, uuid: 'hallo2222' },
            { id: 40, uuid: 'hallo' },
            { id: 50, uuid: 'hallo22' },
          ]}
        />
        <Input placeholder="omar" label="hi" name="name" />
        <TextArea label="sadkljf;asd" name="note" />
        <Switch name={'salah'} />
      </FormWrapper>
      <Switch isActive name={'alaa'} reqName="alaa/trest" />
      <h1 className="text-primary">hi</h1>
      <Link href="/dashboard/test" className="text-primary">
        Test section
      </Link>
      <p className="text-text">{/* This text is rendered on the server: {t("hello")} */}</p>
      <button onClick={() => revalidate('/countries')}>test</button>
      <button onClick={login}>login</button>
      <button onClick={fetchData}>fetch</button>
    </main>
  );
}
