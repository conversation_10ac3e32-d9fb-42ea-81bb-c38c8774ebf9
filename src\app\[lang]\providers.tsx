'use client';

// 3rd party libraries
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { PropsWithChildren, useState } from 'react';

// Components
import ToastComponent from '@/components/ui/Toast';

interface IProps extends PropsWithChildren {
  session: any;
}

const Providers = ({ session, children }: IProps) => {
  const [client] = useState(new QueryClient());

  return (
    <QueryClientProvider client={client}>
      <NextThemesProvider attribute="class" defaultTheme="system" enableSystem>
        <SessionProvider session={session}>{children}</SessionProvider>
        <ToastComponent />
        <ReactQueryDevtools initialIsOpen={false} />
      </NextThemesProvider>
    </QueryClientProvider>
  );
};

export default Providers;
