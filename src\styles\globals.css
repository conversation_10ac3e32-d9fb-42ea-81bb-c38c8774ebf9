@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Color palette example 

  --color-error-100: ;
  --color-error-200: ;
  --color-error-300: ;
  --color-error-400: ;
  --color-error-500: ;
  --color-error-600: ;
  --color-error-700: ;
  --color-error-800: ;
  --color-error-900: ; 
*/

@layer base {
  html {
    --color-primary-50: #f9f4ea;
    --color-primary-100: #eeddbd;
    --color-primary-200: #e5cc9d;
    --color-primary-300: #d9b570;
    --color-primary-400: #d2a655;
    --color-primary-500: #c7902a;
    --color-primary-600: #b58326;
    --color-primary-700: #8d661e;
    --color-primary-800: #6d4f17;
    --color-primary-900: #543c12;

    --color-gray-50: #f7f8f9;
    --color-gray-100: #eef0f3;
    --color-gray-200: #ffffff;
    --color-gray-300: #bbc3cf;
    --color-gray-400: #8896ab;
    --color-gray-500: #556987;
    --color-gray-600: #4d5f7a;
    --color-gray-700: #404f65;
    --color-gray-800: #333f51;
    --color-gray-900: #2a3342;

    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;

    --color-error-100: #fdeeec;
    --color-error-200: #fbd6d0;
    --color-error-300: #f9bdb4;
    --color-error-400: #f48b7c;
    --color-error-500: #ef5944;
    --color-error-600: #d7503d;
    --color-error-700: #b34333;
    --color-error-800: #752c21;
    --color-error-900: #752c21;

    --color-warning-100: #fef5e7;
    --color-warning-200: #fde7c2;
    --color-warning-300: #fbd89d;
    --color-warning-400: #f8bb54;
    --color-warning-500: #f59e0b;
    --color-warning-600: #dd8e0a;
    --color-warning-700: #b87708;
    --color-warning-800: #935f07;
    --color-warning-900: #784d05;

    --color-info-100: #cee0fd;
    --color-info-200: #cee0fd;
    --color-info-300: #b1cdfb;
    --color-info-400: #76a8f9;
    --color-info-500: #3b82f6;
    --color-info-600: #3575dd;
    --color-info-700: #2c62b9;
    --color-info-800: #234e94;
    --color-info-900: #1d4079;

    --color-secondary: #4a425d;

    --color-label: var(--color-gray-800);
    --color-disabled: var(--color-gray-300);
    --color-typography: var(--color-gray-500);
    --color-placeholder: var(--color-gray-300);

    --color-bg-card: #f8f8f8;
    --color-bg-disabled: #f8f8f8;
    --color-bg-paper: var(--color-gray-200);
  }

  html[class="dark"] {
    --color-label: var(--color-gray-200);
    --color-typography: var(--color-gray-200);
    --color-placeholder: var(--color-gray-500);

    --color-bg-card: #3d3d3d;
    --color-bg-paper: #282828;
    --color-bg-disabled: var(--color-gray-400);
  }

  .container {
    padding: 1.5rem;
  }
}

body {
  background-color: #f8f8f8;
}

.rdp-root {
  --rdp-accent-color: var(--color-primary-500) !important;
  --rdp-accent-background-color: var(--color-primary-500) !important;
}

.rdp-day.rdp-today.rdp-selected:not(.rdp-range_middle) {
  @apply text-white;
}

.rdp-day.rdp-today.rdp-selected.rdp-range_middle {
  @apply text-black}

html[class="dark"] {
  body {
    background-color: #131313;
  }
}

/* width */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 7px;
  overflow: hidden;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--color-gray-500);
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/*
   Animation example, for spinners
*/
.animate-spin {
  -moz-animation: spin 1.5s infinite linear;
  -o-animation: spin 1.5s infinite linear;
  -webkit-animation: spin 1.5s infinite linear;
  animation: spin 1.5s infinite linear;
  display: inline-block;
}
@-moz-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-o-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-ms-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
